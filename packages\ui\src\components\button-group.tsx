"use client";

import { cn } from "@workspace/ui/lib/utils";
import {
  segmentedContainerClasses,
  segmentedItemBaseClasses,
} from "@workspace/ui/components/tabs";

function ButtonGroup({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      role="group"
      data-slot="button-group"
      className={cn(segmentedContainerClasses, className)}
      {...props}
    />
  );
}

interface ButtonGroupItemProps {
  className?: string;
  children?: React.ReactNode;
  asChild?: boolean;
  type?: "button" | "submit" | "reset";
}

function ButtonGroupItem({
  className,
  children,
  asChild = false,
  type = "button",
  ...props
}: ButtonGroupItemProps &
  (React.ComponentProps<"button"> | React.ComponentProps<"div">)) {
  const baseClasses = cn(
    segmentedItemBaseClasses,
    "active:bg-primary not-active:bg-gradient-to-t not-active:inset-shadow-xs not-active:inset-shadow-ring active:inset-shadow-sm active:inset-shadow-orange-800/50 transition-none active:[&>*]:translate-y-px -outline-offset-10",
    className,
  );

  if (asChild) {
    return (
      <div
        data-slot="button-group-item"
        className={baseClasses}
        {...(props as React.ComponentProps<"div">)}
      >
        <div>{children}</div>
      </div>
    );
  }

  return (
    <button
      data-slot="button-group-item"
      type={type}
      className={baseClasses}
      {...(props as React.ComponentProps<"button">)}
    >
      <div>{children}</div>
    </button>
  );
}

export { ButtonGroup, ButtonGroupItem };
