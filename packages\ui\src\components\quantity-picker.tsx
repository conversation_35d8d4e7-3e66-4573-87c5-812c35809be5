"use client";

import { useRef } from "react";
import { Minus, Plus } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { Input } from "@workspace/ui/components/input";
import {
  ButtonGroup,
  ButtonGroupItem,
} from "@workspace/ui/components/button-group";

export interface QuantityPickerProps {
  value: number;
  onChange: (value: number) => void;
  onBlur?: () => void;
  min?: number;
  max?: number;
  label?: string;
  className?: string;
  disabled?: boolean;
}

export function QuantityPicker({
  value,
  onChange,
  onBlur,
  min = 1,
  max = 20,
  label = "adet",
  className,
  disabled = false,
}: QuantityPickerProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDecrement = () => {
    if (disabled) return;
    const newValue = Math.max(min, value - 1);
    onChange(newValue);
    onBlur?.();
  };

  const handleIncrement = () => {
    if (disabled) return;
    const newValue = Math.min(max, value + 1);
    onChange(newValue);
    onBlur?.();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;
    const inputValue = parseInt(e.target.value);
    if (!isNaN(inputValue) && inputValue >= min && inputValue <= max) {
      onChange(inputValue);
    }
  };

  const handleInputContainerClick = () => {
    if (disabled) return;
    inputRef.current?.focus();
  };

  return (
    <ButtonGroup className={className}>
      <ButtonGroupItem
        onClick={handleDecrement}
        disabled={disabled || value <= min}
      >
        <Minus />
      </ButtonGroupItem>
      <ButtonGroupItem
        asChild
        className={cn(
          "p-0 focus-within:outline-4 -outline-offset-3 cursor-text",
          disabled && "opacity-50 cursor-not-allowed",
        )}
        onClick={handleInputContainerClick}
      >
        <div className="flex items-end gap-1.5 pl-0 pr-2.5 py-2">
          <Input
            ref={inputRef}
            type="number"
            value={value}
            onChange={handleInputChange}
            onBlur={onBlur}
            disabled={disabled}
            min={min}
            max={max}
            className="w-22 h-full p-0 bg-transparent border-0 md:text-lg text-right outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          />
          <span className="w-full text-left">{label}</span>
        </div>
      </ButtonGroupItem>

      <ButtonGroupItem
        onClick={handleIncrement}
        disabled={disabled || value >= max}
      >
        <Plus />
      </ButtonGroupItem>
    </ButtonGroup>
  );
}
