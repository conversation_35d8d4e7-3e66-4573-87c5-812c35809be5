"use client";

import { useRef } from "react";
import { Minus, Plus } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { Input } from "@workspace/ui/components/input";
import {
  ButtonGroup,
  ButtonGroupItem,
} from "@workspace/ui/components/button-group";

export interface QuantityPickerProps {
  value: number;
  onChange: (value: number) => void;
  onBlur?: () => void;
  min?: number;
  max?: number;
  label?: string;
  className?: string;
  disabled?: boolean;
}

export function QuantityPicker({
  value,
  onChange,
  onBlur,
  min = 1,
  max = 20,
  label = "adet",
  className,
  disabled = false,
}: QuantityPickerProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDecrement = () => {
    if (disabled) return;
    const newValue = Math.max(min, value - 1);
    onChange(newValue);
    onBlur?.();
  };

  const handleIncrement = () => {
    if (disabled) return;
    const newValue = Math.min(max, value + 1);
    onChange(newValue);
    onBlur?.();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;
    const inputValue = parseInt(e.target.value);
    if (!isNaN(inputValue) && inputValue >= min && inputValue <= max) {
      onChange(inputValue);
    }
  };

  const handleInputContainerClick = () => {
    if (disabled) return;
    const input = inputRef.current;
    if (input) {
      input.focus();
      // For number inputs, we need to use a workaround since setSelectionRange doesn't work
      // We'll temporarily change the type to text, set cursor position, then change back
      setTimeout(() => {
        const currentType = input.type;
        const currentValue = input.value;

        // Temporarily change to text type
        input.type = "text";

        // Set cursor to end
        const length = currentValue.length;
        input.setSelectionRange(length, length);

        // Change back to number type
        input.type = currentType;
      }, 0);
    }
  };

  return (
    <ButtonGroup className={className}>
      <ButtonGroupItem
        onClick={handleDecrement}
        disabled={disabled || value <= min}
      >
        <Minus />
      </ButtonGroupItem>
      <ButtonGroupItem
        asChild
        className={cn(
          "p-0 focus-within:outline-4 -outline-offset-3 cursor-text",
          disabled && "opacity-50 cursor-not-allowed",
        )}
        onClick={handleInputContainerClick}
      >
        <div className="flex items-end gap-1.5 pl-0 pr-2.5 py-2">
          <Input
            ref={inputRef}
            type="number"
            value={value}
            onChange={handleInputChange}
            onBlur={onBlur}
            disabled={disabled}
            min={min}
            max={max}
            className="w-22 h-full p-0 bg-transparent border-0 md:text-lg text-right outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          />
          <span className="w-full text-left">{label}</span>
        </div>
      </ButtonGroupItem>

      <ButtonGroupItem
        onClick={handleIncrement}
        disabled={disabled || value >= max}
      >
        <Plus />
      </ButtonGroupItem>
    </ButtonGroup>
  );
}
