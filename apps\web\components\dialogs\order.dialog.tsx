"use client";

import Image from "next/image";
import { z } from "zod";
import { useForm, revalidateLogic } from "@tanstack/react-form";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@workspace/ui/components/dialog";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  <PERSON>bsContent,
} from "@workspace/ui/components/tabs";
import {
  StepperVertical,
  Step,
  StepContentActive,
  StepContentInactive,
  StepActivationButton,
  type StepConfig,
} from "@workspace/ui/components/stepper-vertical";
import { StarRating } from "@workspace/ui/components/star-rating";
import { Search } from "@workspace/ui/components/search";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { Label } from "@workspace/ui/components/label";

import { Separator } from "@workspace/ui/components/separator";
import { Badge } from "@workspace/ui/components/badge";
import { PlaceholderUser } from "@workspace/ui/components/icons/placeholder-user";
import { Filter, Clock, Plus } from "lucide-react";
import { QuantityPicker } from "@workspace/ui/components/quantity-picker";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
} from "@workspace/ui/components/pagination";
import { cn } from "@workspace/ui/lib/utils";
import { createContext, useContext, useState } from "react";

// Zod schema for order form validation
const orderFormSchema = z.object({
  // Step 1: Service Selection
  selectedService: z
    .string()
    .min(1, "Bir servis seçmelisiniz")
    .nullable()
    .refine((val) => val !== null, "Bir servis seçmelisiniz"),

  // Step 2: Extras Selection (optional)
  selectedExtras: z.array(z.string()).default([]),

  // Step 3: Quantity and Details
  quantity: z
    .number()
    .min(1, "Miktar en az 1 olmalıdır")
    .max(20, "Miktar en fazla 20 olabilir")
    .int("Miktar tam sayı olmalıdır"),
});

export type OrderFormData = z.infer<typeof orderFormSchema>;

// Context for Order Dialog
interface OrderDialogContext {
  // Service data
  serviceImage: string;
  serviceTitle: string;
  serviceDescription: string;
  overallRating: number;

  // Activity details
  rank: string;
  server: string;
  character: string;
  username: string;
  nickname: string;

  // Customization
  quantityLabel: string;

  // Order state
  activeTab: string;
  setActiveTab: (tab: string) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  selectedService: string | null;
  setSelectedService: (serviceId: string | null) => void;
  selectedExtras: string[];
  setSelectedExtras: React.Dispatch<React.SetStateAction<string[]>>;
  quantity: number;
  setQuantity: (quantity: number) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  reviewsPage: number;
  setReviewsPage: (page: number) => void;
  questionsPage: number;
  setQuestionsPage: (page: number) => void;

  // Data
  serviceOptions: ServiceOption[];
  extraOptions: ExtraOption[];
  reviews: Review[];
  questions: Question[];

  // Functions
  calculateTotalPrice: () => number;
  handleServiceSelect: (serviceId: string) => void;
  handleExtraToggle: (extraId: string) => void;
  handleOrderSubmit: () => void;
}

const OrderDialogContext = createContext<OrderDialogContext | null>(null);

export const useOrderDialog = () => {
  const context = useContext(OrderDialogContext);
  if (!context) {
    throw new Error("useOrderDialog must be used within OrderDialogProvider");
  }
  return context;
};

export interface ServiceOption {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
}

export interface ExtraOption {
  id: string;
  name: string;
  price: number;
  selected?: boolean;
}

export interface Review {
  id: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  comment: string;
  date: string;
}

export interface Question {
  id: string;
  question: string;
  answer?: string;
  userName: string;
  date: string;
}

export interface OrderDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;

  // Service data
  serviceImage?: string;
  serviceTitle?: string;
  serviceDescription?: string;
  overallRating?: number;

  // Activity details
  rank?: string;
  server?: string;
  character?: string;
  username?: string;

  // Order data
  serviceOptions?: ServiceOption[];
  extraOptions?: ExtraOption[];
  reviews?: Review[];
  questions?: Question[];

  // Customization
  quantityLabel?: string;

  // Event handlers
  onOrderSubmit?: (orderData: any) => void;
}

const getOrderSteps = (
  currentStep: number,
  formState: any,
  completedSteps: Set<number>,
): StepConfig[] => {
  // Helper function to check if a field is completed (valid and dirty)
  const isFieldCompleted = (fieldName: string) => {
    const field = formState.fieldMeta[fieldName];
    if (!field) return false;

    // Field must be dirty (user has interacted) and have no errors
    return field.isDirty && field.errors.length === 0;
  };

  // Check if service field is completed
  const serviceCompleted =
    isFieldCompleted("selectedService") &&
    formState.values.selectedService !== null;

  // Extras step is always considered completed since it's optional
  // But we check if user has interacted with it
  const extrasCompleted = isFieldCompleted("selectedExtras");

  // Details step requires quantity to be valid and dirty
  const detailsCompleted = isFieldCompleted("quantity");

  return [
    {
      id: "service",
      label: "Servis Seç",
      completed: serviceCompleted || completedSteps.has(0),
    },
    {
      id: "extras",
      label: "Ekstra Ekle",
      completed: extrasCompleted || currentStep > 1 || completedSteps.has(1),
    },
    {
      id: "details",
      label: "Miktar ve Teslimat",
      completed: detailsCompleted || completedSteps.has(2),
    },
  ];
};

// Order Tab Component
const OrderTab: React.FC = () => {
  const contextHandlers = useOrderDialog();
  const {
    currentStep,
    setCurrentStep,
    selectedService,
    serviceOptions,
    selectedExtras,
    extraOptions,
    quantity,
    quantityLabel,
    calculateTotalPrice,
    handleOrderSubmit,
  } = contextHandlers;

  // Track completed steps when next button is clicked
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  // Initialize TanStack Form
  const form = useForm({
    defaultValues: {
      selectedService: selectedService,
      selectedExtras: selectedExtras,
      quantity: quantity,
    },
    validationLogic: revalidateLogic({
      mode: "blur",
      modeAfterSubmission: "change",
    }),
    onSubmit: async ({ value }) => {
      handleOrderSubmit();
    },
  });

  // Sync form values with context state
  const handleServiceSelect = (serviceId: string) => {
    form.setFieldValue("selectedService", serviceId);
    contextHandlers.handleServiceSelect(serviceId); // Update context state
  };

  const handleExtraToggle = (extraId: string) => {
    const currentExtras = form.getFieldValue("selectedExtras");
    const newExtras = currentExtras.includes(extraId)
      ? currentExtras.filter((id: string) => id !== extraId)
      : [...currentExtras, extraId];
    form.setFieldValue("selectedExtras", newExtras);
    contextHandlers.handleExtraToggle(extraId); // Update context state
  };

  const handleQuantityChange = (newQuantity: number) => {
    form.setFieldValue("quantity", newQuantity);
    contextHandlers.setQuantity(newQuantity); // Update context state
  };

  return (
    <TabsContent value="siparis" className="mt-0 h-full flex flex-col flex-1">
      <div className="flex-1 scrollbar overflow-y-auto pr-4">
        <StepperVertical
          steps={getOrderSteps(currentStep, form.state, completedSteps)}
          currentStep={currentStep}
          onStepChange={setCurrentStep}
          autoScroll={true}
        >
          {/* Step 1: Service Selection */}
          <Step stepId="service">
            <StepContentActive>
              <form.Field
                name="selectedService"
                validators={{
                  onChange: ({ value }) => {
                    if (!value) return "Bir servis seçmelisiniz";
                    return undefined;
                  },
                }}
              >
                {(field) => (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Servis Seç</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {serviceOptions.map((service) => (
                        <button
                          key={service.id}
                          type="button"
                          onClick={() => {
                            handleServiceSelect(service.id);
                            field.handleBlur();
                          }}
                          className={cn(
                            "p-5 border-2 border-border rounded-lg text-left bg-background",
                            field.state.value === service.id
                              ? "ring-primary ring-3"
                              : "border-border hover:border-border-foreground",
                          )}
                        >
                          <div className="space-y-2">
                            <div className="flex gap-2 justify-between items-start">
                              <h4 className="font-medium text-base">
                                {service.name}
                              </h4>
                              <Badge variant="default">
                                {service.price} SODA
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {service.description}
                            </p>
                          </div>
                        </button>
                      ))}
                    </div>
                    {field.state.meta.isTouched &&
                      field.state.meta.errors.length > 0 && (
                        <p className="text-sm text-destructive">
                          {field.state.meta.errors[0]}
                        </p>
                      )}
                  </div>
                )}
              </form.Field>
            </StepContentActive>
            <StepContentInactive>
              <div className="space-y-3 p-4 bg-background rounded-lg border-2 border-border">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-foreground">
                      {"Servis Seçildi"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {selectedService &&
                        serviceOptions.find((s) => s.id === selectedService)
                          ?.name}
                      {" ("}
                      {selectedService &&
                        serviceOptions.find((s) => s.id === selectedService)
                          ?.price}{" "}
                      SODA{")"}
                    </p>
                  </div>
                  <StepActivationButton stepId="service">
                    {"DÜZENLE"}
                  </StepActivationButton>
                </div>
              </div>
            </StepContentInactive>
          </Step>

          {/* Step 2: Extras Selection */}
          <Step stepId="extras">
            <StepContentActive>
              <form.Field name="selectedExtras">
                {(field) => (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Ekstra Ekle</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {extraOptions.map((extra) => (
                        <div
                          key={extra.id}
                          className={cn(
                            "flex items-center space-x-3 p-3 border-2 border-border rounded-lg bg-background",
                            field.state.value.includes(extra.id) &&
                              "ring-2 ring-primary",
                          )}
                        >
                          <Checkbox
                            id={extra.id}
                            checked={field.state.value.includes(extra.id)}
                            onCheckedChange={() => {
                              handleExtraToggle(extra.id);
                              field.handleBlur();
                            }}
                            size="large"
                          />
                          <Label
                            htmlFor={extra.id}
                            className="flex-1 cursor-pointer"
                          >
                            <div className="flex-1 flex justify-between items-center">
                              <span>{extra.name}</span>
                              <Badge variant="default">
                                +{extra.price} SODA
                              </Badge>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </form.Field>
            </StepContentActive>
            <StepContentInactive>
              <div className="space-y-5 p-4 bg-background rounded-lg border-2 border-border">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-foreground">
                      {selectedExtras.length > 0
                        ? "Ekstra Eklendi"
                        : "Ekstra Ekle"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {selectedExtras.length > 0
                        ? `${selectedExtras.length} seçenek seçildi`
                        : "Ekstra seçenek eklenmedi"}
                    </p>
                  </div>
                  <StepActivationButton stepId="extras">
                    {selectedExtras.length > 0 ? "DÜZENLE" : "EKLE"}
                  </StepActivationButton>
                </div>
                {selectedExtras.length > 0 && (
                  <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs text-muted-foreground">
                    {selectedExtras.map((extraId) => {
                      const extra = extraOptions.find((e) => e.id === extraId);
                      return extra ? (
                        <p key={extraId}>
                          • {extra.name} (+{extra.price} SODA)
                        </p>
                      ) : null;
                    })}
                  </div>
                )}
              </div>
            </StepContentInactive>
          </Step>

          {/* Step 3: Quantity and Duration */}
          <Step stepId="details">
            <StepContentActive>
              <form.Field
                name="quantity"
                validators={{
                  onChange: ({ value }) => {
                    if (value < 1) return "Miktar en az 1 olmalıdır";
                    if (value > 20) return "Miktar en fazla 20 olabilir";
                    if (!Number.isInteger(value))
                      return "Miktar tam sayı olmalıdır";
                    return undefined;
                  },
                }}
              >
                {(field) => (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">
                      Aktivite Süresi Belirle
                    </h3>
                    <div className="fake-text-stroke-muted">
                      <div className="space-y-4">
                        <p className="text-sm leading-6 text-muted-foreground w-5/6">
                          {`Her bir ${quantityLabel} 5 saatlik aktivite süresine denk
                          gelmektedir. Örneğin 2 adet seçimi 10 saatlik
                          aktivite anlamına gelir.`}
                        </p>
                        <div className="flex items-center gap-2">
                          <QuantityPicker
                            value={field.state.value}
                            onChange={(value) => {
                              handleQuantityChange(value);
                              field.handleBlur();
                            }}
                            onBlur={field.handleBlur}
                            min={1}
                            max={20}
                            label={quantityLabel}
                          />
                        </div>
                        <p className="text-sm font-semibold pt-2">
                          Toplam aktivite süresi: {field.state.value * 5} saat
                        </p>
                        {field.state.meta.isTouched &&
                          field.state.meta.errors.length > 0 && (
                            <p className="text-sm text-destructive">
                              {field.state.meta.errors[0]}
                            </p>
                          )}
                      </div>
                    </div>
                  </div>
                )}
              </form.Field>
            </StepContentActive>
            <StepContentInactive>
              <div className="space-y-3 p-4 bg-background rounded-lg border-2 border-border">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-foreground">
                      {"Aktivite Süresi Belirlendi"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {quantity} {quantityLabel} girildi
                    </p>
                  </div>
                  <StepActivationButton stepId="details">
                    {"DÜZENLE"}
                  </StepActivationButton>
                </div>
              </div>
            </StepContentInactive>
          </Step>
        </StepperVertical>
      </div>

      <Separator />

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-4 px-6">
        <div>
          <p className="text-sm text-muted-foreground">{"Sipariş Tutarı:"}</p>
          <p className="text-2xl font-bold">{calculateTotalPrice()} SODA</p>
        </div>

        {currentStep < 2 ? (
          <Button
            size="medium"
            variant="primary"
            onClick={() => {
              // Mark current step as completed when next button is clicked
              setCompletedSteps((prev) => new Set(prev).add(currentStep));
              setCurrentStep(Math.min(2, currentStep + 1));
            }}
            disabled={currentStep === 0 && !selectedService}
          >
            DEVAM
          </Button>
        ) : (
          <Button
            size="medium"
            variant="primary"
            onClick={handleOrderSubmit}
            disabled={!selectedService}
          >
            SİPARİŞ VER
          </Button>
        )}
      </div>
    </TabsContent>
  );
};

// Reviews Tab Component
const ReviewsTab: React.FC = () => {
  const { searchQuery, setSearchQuery, reviews, reviewsPage, setReviewsPage } =
    useOrderDialog();

  const itemsPerPage = 3;

  const getPageNumbers = (currentPage: number, totalPages: number) => {
    const pages: (number | "ellipsis")[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      if (currentPage > 3) {
        pages.push("ellipsis");
      }
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      if (currentPage < totalPages - 2) {
        pages.push("ellipsis");
      }
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    return pages;
  };

  const paginatedReviews = reviews.slice(
    (reviewsPage - 1) * itemsPerPage,
    reviewsPage * itemsPerPage,
  );

  return (
    <TabsContent
      value="degerlendirme"
      className="mt-0 h-full flex flex-col space-y-4 flex-1"
    >
      {/* Search and Filter */}
      <div className="flex gap-2">
        <div className="flex-1">
          <Search
            placeholder="Değerlendirmelerde ara..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
        </div>
        <Button size="small" variant="secondary">
          <Filter className="size-4" />
        </Button>
      </div>

      {/* Reviews List */}
      <div className="flex-1 overflow-y-auto space-y-4 scrollbar">
        {paginatedReviews.map((review) => (
          <div key={review.id} className="p-4 border-2 rounded-lg space-y-3">
            <div className="flex items-start gap-3">
              <div className="size-10 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                {review.userAvatar ? (
                  <Image
                    src={review.userAvatar}
                    alt={review.userName}
                    width={40}
                    height={40}
                    className="size-10 rounded-full object-cover"
                  />
                ) : (
                  <PlaceholderUser className="size-6" />
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{review.userName}</h4>
                  <StarRating value={review.rating} size="small" />
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {review.date}
                </p>
              </div>
            </div>
            <p className="text-sm">{review.comment}</p>
            <div className="flex items-center gap-2 pt-2">
              <Button size="small" variant="secondary">
                👍 Faydalı
              </Button>
              <Button size="small" variant="secondary">
                👎 Faydalı değil
              </Button>
              <Button size="small" variant="secondary">
                💬 Yanıtla
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <Pagination>
        <PaginationContent className="flex justify-between gap-2 w-full">
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (reviewsPage > 1) setReviewsPage(reviewsPage - 1);
              }}
              className={
                reviewsPage === 1 ? "pointer-events-none opacity-50" : ""
              }
            />
          </PaginationItem>

          <div className="flex gap-2">
            {getPageNumbers(
              reviewsPage,
              Math.ceil(reviews.length / itemsPerPage),
            ).map((page, index) => (
              <PaginationItem key={index}>
                {page === "ellipsis" ? (
                  <PaginationEllipsis />
                ) : (
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      setReviewsPage(page as number);
                    }}
                    isActive={reviewsPage === page}
                    size="icon"
                  >
                    {page}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}
          </div>

          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (reviewsPage < Math.ceil(reviews.length / itemsPerPage)) {
                  setReviewsPage(reviewsPage + 1);
                }
              }}
              className={
                reviewsPage === Math.ceil(reviews.length / itemsPerPage)
                  ? "pointer-events-none opacity-50"
                  : ""
              }
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </TabsContent>
  );
};

// Questions Tab Component
const QuestionsTab: React.FC = () => {
  const {
    searchQuery,
    setSearchQuery,
    questions,
    questionsPage,
    setQuestionsPage,
  } = useOrderDialog();

  const itemsPerPage = 3;

  const getPageNumbers = (currentPage: number, totalPages: number) => {
    const pages: (number | "ellipsis")[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      if (currentPage > 3) {
        pages.push("ellipsis");
      }
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      if (currentPage < totalPages - 2) {
        pages.push("ellipsis");
      }
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    return pages;
  };

  const paginatedQuestions = questions.slice(
    (questionsPage - 1) * itemsPerPage,
    questionsPage * itemsPerPage,
  );

  return (
    <TabsContent
      value="soru-cevap"
      className="mt-0 h-full flex flex-col space-y-4 flex-1"
    >
      {/* Search and Ask Question */}
      <div className="flex gap-2">
        <div className="flex-1">
          <Search
            placeholder="Sorularda ara..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
        </div>
        <Button size="small" variant="primary">
          <Plus className="size-4" />
          Soru Sor
        </Button>
      </div>

      {/* Questions List */}
      <div className="flex-1 overflow-y-auto space-y-4 scrollbar">
        {paginatedQuestions.map((qa) => (
          <div key={qa.id} className="p-4 border-2 rounded-lg space-y-3">
            <div>
              <h4 className="font-medium">{qa.question}</h4>
              <p className="text-sm text-muted-foreground">
                {qa.userName} • {qa.date}
              </p>
            </div>
            {qa.answer && (
              <div className="pl-4 border-l-2 border-primary/20">
                <p className="text-sm">{qa.answer}</p>
              </div>
            )}
            <div className="flex items-center gap-2 pt-2">
              <Button size="small" variant="secondary">
                👍 Faydalı
              </Button>
              <Button size="small" variant="secondary">
                👎 Faydalı değil
              </Button>
              {!qa.answer && (
                <Button size="small" variant="primary">
                  💬 Cevapla
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <Pagination>
        <PaginationContent className="flex justify-between gap-2 w-full">
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (questionsPage > 1) setQuestionsPage(questionsPage - 1);
              }}
              className={
                questionsPage === 1 ? "pointer-events-none opacity-50" : ""
              }
            />
          </PaginationItem>

          <div className="flex gap-2">
            {getPageNumbers(
              questionsPage,
              Math.ceil(questions.length / itemsPerPage),
            ).map((page, index) => (
              <PaginationItem key={index}>
                {page === "ellipsis" ? (
                  <PaginationEllipsis />
                ) : (
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      setQuestionsPage(page as number);
                    }}
                    isActive={questionsPage === page}
                    size="icon"
                  >
                    {page}
                  </PaginationLink>
                )}
              </PaginationItem>
            ))}
          </div>

          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (
                  questionsPage < Math.ceil(questions.length / itemsPerPage)
                ) {
                  setQuestionsPage(questionsPage + 1);
                }
              }}
              className={
                questionsPage === Math.ceil(questions.length / itemsPerPage)
                  ? "pointer-events-none opacity-50"
                  : ""
              }
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </TabsContent>
  );
};

const ServiceSummaryColumn: React.FC = () => {
  const {
    serviceImage,
    serviceTitle,
    serviceDescription,
    overallRating,
    rank,
    server,
    character,
    nickname,
  } = useOrderDialog();

  return (
    <div className="w-1/3 flex flex-col gap-5 p-4">
      {/* Service Image */}
      <div className="aspect-video relative rounded-lg overflow-hidden border-2 border-background ring-2 ring-foreground shrink-0 mb-2">
        <Image
          src={serviceImage}
          alt={serviceTitle}
          fill
          className="object-cover"
        />
        <div className="absolute bottom-2 right-2">
          <StarRating value={overallRating} size="large" />
        </div>
        <div className="absolute top-3 left-3">
          <Image
            src="/mocks/icon/valorant-icon.jpg"
            alt="Valorant"
            width={100}
            height={100}
            className="shrink-0 size-10 rounded-sm ring-2 ring-foreground border-2 border-background"
          />
        </div>
      </div>

      {/* Service Info */}
      <div className="text-left">
        <p className="text-sm text-muted-foreground max-h-[150px] leading-6 overflow-y-auto scrollbar pr-2">
          {serviceDescription}
        </p>
      </div>

      <Separator />

      {/* Activity Details */}
      <div className="space-y-3">
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Rank:</span>
            <span>{rank}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Sunucu:</span>
            <span>{server}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Karakter:</span>
            <span>{character}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Kullanıcı Adı:</span>
            <span>{nickname}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const OrderTabsColumn: React.FC = () => {
  const { activeTab, setActiveTab } = useOrderDialog();

  return (
    <div className="flex-1 flex flex-col">
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex flex-col h-full"
      >
        <div className="px-2">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="siparis">{"SİPARİŞ"}</TabsTrigger>
            <TabsTrigger value="degerlendirme">{"DEĞERLENDİRME"}</TabsTrigger>
            <TabsTrigger value="soru-cevap">{"SORU-CEVAP"}</TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 overflow-hidden mt-4 bg-muted/50 rounded-lg p-8 flex flex-col">
          <OrderTab />
          <ReviewsTab />
          <QuestionsTab />
        </div>
      </Tabs>
    </div>
  );
};

export function OrderDialog({
  open = false,
  onOpenChange,
  serviceImage = "/dialogs/order/16995404846901826.webp",
  serviceTitle = "Valorant Boost Hizmeti",
  serviceDescription = "Profesyonel oyuncularımızla rank yükseltme hizmeti",
  overallRating = 4.5,
  rank = "Diamond 2",
  server = "EU West",
  character = "Jett Main",
  username = "GeceYıldızı",
  serviceOptions = [],
  extraOptions = [],
  reviews = [],
  questions = [],
  quantityLabel = "turnuva",
  onOrderSubmit,
}: OrderDialogProps) {
  const [activeTab, setActiveTab] = useState("siparis");
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [selectedExtras, setSelectedExtras] = useState<string[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [reviewsPage, setReviewsPage] = useState(1);
  const [questionsPage, setQuestionsPage] = useState(1);

  const handleServiceSelect = (serviceId: string) => {
    setSelectedService(serviceId);
  };

  const handleExtraToggle = (extraId: string) => {
    setSelectedExtras((prev) =>
      prev.includes(extraId)
        ? prev.filter((id) => id !== extraId)
        : [...prev, extraId],
    );
  };

  const calculateTotalPrice = () => {
    let total = 0;

    // Add service price
    if (selectedService) {
      const service = serviceOptions.find((s) => s.id === selectedService);
      if (service) total += service.price;
    }

    // Add extras price
    selectedExtras.forEach((extraId) => {
      const extra = extraOptions.find((e) => e.id === extraId);
      if (extra) total += extra.price;
    });

    // Multiply by quantity
    return total * quantity;
  };

  const handleOrderSubmitInternal = () => {
    const orderData = {
      service: selectedService,
      extras: selectedExtras,
      quantity,
      totalPrice: calculateTotalPrice(),
    };

    if (onOrderSubmit) {
      onOrderSubmit(orderData);
    } else {
      alert("Sipariş başarıyla gönderildi!");
    }
  };

  const contextValue: OrderDialogContext = {
    // Service data
    serviceImage: serviceImage!,
    serviceTitle: serviceTitle!,
    serviceDescription: serviceDescription!,
    overallRating: overallRating!,

    // Activity details
    rank: rank!,
    server: server!,
    character: character!,
    username: username!,
    nickname: `${username!}#${Math.floor(1000 + Math.random() * 9000)}`,

    // Customization
    quantityLabel: quantityLabel!,

    // Order state
    activeTab,
    setActiveTab,
    currentStep,
    setCurrentStep,
    selectedService,
    setSelectedService,
    selectedExtras,
    setSelectedExtras,
    quantity,
    setQuantity,
    searchQuery,
    setSearchQuery,
    reviewsPage,
    setReviewsPage,
    questionsPage,
    setQuestionsPage,

    // Data
    serviceOptions,
    extraOptions,
    reviews,
    questions,

    // Functions
    calculateTotalPrice,
    handleServiceSelect,
    handleExtraToggle,
    handleOrderSubmit: handleOrderSubmitInternal,
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[1200px] h-[80vh] max-h-[900px] overflow-hidden w-full max-w-full flex flex-col"
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <OrderDialogContext.Provider value={contextValue}>
          <DialogHeader>
            <DialogTitle>
              {username} ile {serviceTitle}
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-1 gap-8 overflow-hidden px-6 pt-2">
            <ServiceSummaryColumn />
            <OrderTabsColumn />
          </div>

          <DialogFooter className="mt-0" />
        </OrderDialogContext.Provider>
      </DialogContent>
    </Dialog>
  );
}
